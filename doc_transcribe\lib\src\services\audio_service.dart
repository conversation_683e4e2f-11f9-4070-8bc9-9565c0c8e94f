import 'dart:async';
import 'dart:io';
import 'dart:typed_data';
import 'package:record/record.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;
import 'storage_service.dart';

enum AudioFormat {
  wav,
  aac,
  opus,
}

class AudioService {
  static final AudioService _instance = AudioService._internal();
  static AudioService get instance => _instance;
  AudioService._internal();

  AudioRecorder? _recorder;
  StreamSubscription? _recordingDataSubscription;
  StreamController<Uint8List>? _audioStreamController;
  String? _currentRecordingPath;
  bool _isInitialized = false;
  bool _isRecording = false;
  bool _isPaused = false;

  // Audio configuration
  static const int sampleRate = 16000; // 16kHz for Riva
  static const int numChannels = 1; // Mono
  static const int bitRate = 16000; // 16-bit

  Stream<Uint8List>? get audioStream => _audioStreamController?.stream;
  bool get isInitialized => _isInitialized;
  bool get isRecording => _isRecording;
  bool get isPaused => _isPaused;
  String? get currentRecordingPath => _currentRecordingPath;

  // Initialize audio service
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // Request microphone permission
      await _requestPermissions();

      // Initialize recorder
      _recorder = AudioRecorder();

      _isInitialized = true;
    } catch (e) {
      throw Exception('Failed to initialize audio service: $e');
    }
  }

  // Request necessary permissions
  Future<void> _requestPermissions() async {
    final microphoneStatus = await Permission.microphone.request();
    if (microphoneStatus != PermissionStatus.granted) {
      throw Exception('Microphone permission is required for recording');
    }

    if (Platform.isAndroid) {
      final storageStatus = await Permission.storage.request();
      if (storageStatus != PermissionStatus.granted) {
        throw Exception('Storage permission is required for saving recordings');
      }
    }
  }

  // Start recording
  Future<void> startRecording({
    String? consultationId,
    AudioFormat format = AudioFormat.wav,
    bool enableStreaming = true,
  }) async {
    if (!_isInitialized) {
      throw Exception('Audio service not initialized');
    }

    if (_isRecording) {
      throw Exception('Already recording');
    }

    try {
      // Generate file path
      _currentRecordingPath = await _generateRecordingPath(consultationId, format);

      // Configure encoder based on format
      AudioEncoder encoder;
      switch (format) {
        case AudioFormat.wav:
          encoder = AudioEncoder.wav;
          break;
        case AudioFormat.aac:
          encoder = AudioEncoder.aacLc;
          break;
        case AudioFormat.opus:
          encoder = AudioEncoder.opus;
          break;
      }

      // Setup audio streaming if enabled
      if (enableStreaming) {
        _audioStreamController = StreamController<Uint8List>.broadcast();
      }

      // Start recording
      await _recorder!.start(
        RecordConfig(
          encoder: encoder,
          sampleRate: sampleRate,
          numChannels: numChannels,
          bitRate: bitRate,
        ),
        path: _currentRecordingPath!,
      );

      // Setup streaming simulation if enabled
      if (enableStreaming && _audioStreamController != null) {
        _recordingDataSubscription = Stream.periodic(
          const Duration(milliseconds: 100),
          (i) => i,
        ).listen((event) {
          _simulateAudioData();
        });
      }

      _isRecording = true;
      _isPaused = false;
    } catch (e) {
      await _cleanup();
      throw Exception('Failed to start recording: $e');
    }
  }

  // Stop recording
  Future<String?> stopRecording() async {
    if (!_isRecording) {
      throw Exception('Not currently recording');
    }

    try {
      await _recorder!.stop();
      await _recordingDataSubscription?.cancel();
      await _audioStreamController?.close();

      _isRecording = false;
      _isPaused = false;
      _recordingDataSubscription = null;
      _audioStreamController = null;

      final recordingPath = _currentRecordingPath;
      _currentRecordingPath = null;

      return recordingPath;
    } catch (e) {
      throw Exception('Failed to stop recording: $e');
    }
  }

  // Pause recording
  Future<void> pauseRecording() async {
    if (!_isRecording || _isPaused) {
      throw Exception('Cannot pause recording in current state');
    }

    try {
      await _recorder!.pause();
      _isPaused = true;
    } catch (e) {
      throw Exception('Failed to pause recording: $e');
    }
  }

  // Resume recording
  Future<void> resumeRecording() async {
    if (!_isRecording || !_isPaused) {
      throw Exception('Cannot resume recording in current state');
    }

    try {
      await _recorder!.resume();
      _isPaused = false;
    } catch (e) {
      throw Exception('Failed to resume recording: $e');
    }
  }

  // Play audio file (simplified - would need audio player package)
  Future<void> playAudioFile(String filePath) async {
    if (!_isInitialized) {
      throw Exception('Audio service not initialized');
    }

    // Note: Audio playback would require additional package like audioplayers
    throw UnimplementedError('Audio playback not implemented in this demo');
  }

  // Stop playing
  Future<void> stopPlaying() async {
    // Note: Audio playback would require additional package like audioplayers
    throw UnimplementedError('Audio playback not implemented in this demo');
  }

  // Get audio file duration
  Future<Duration?> getAudioDuration(String filePath) async {
    try {
      final file = File(filePath);
      if (!await file.exists()) return null;

      // This is a simplified implementation
      // In a real app, you would use a proper audio metadata library
      final stats = await file.stat();
      final fileSizeBytes = stats.size;
      
      // Estimate duration based on file size and audio format
      // For 16kHz, 16-bit, mono WAV: ~32KB per second
      final estimatedSeconds = fileSizeBytes / (sampleRate * 2); // 2 bytes per sample
      return Duration(seconds: estimatedSeconds.round());
    } catch (e) {
      return null;
    }
  }

  // Get current recording level (0.0 to 1.0)
  double getCurrentRecordingLevel() {
    if (!_isRecording || _isPaused) return 0.0;
    
    // In a real implementation, this would return actual audio level
    // For now, we'll simulate it
    return (DateTime.now().millisecondsSinceEpoch % 100) / 100.0;
  }

  // Private methods
  Future<String> _generateRecordingPath(String? consultationId, AudioFormat format) async {
    final storageService = StorageService.instance;
    
    if (consultationId != null) {
      return storageService.getAudioFilePath(consultationId);
    } else {
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final extension = _getFileExtension(format);
      return path.join(
        storageService.audioDirectory.path,
        'recording_$timestamp.$extension',
      );
    }
  }

  String _getFileExtension(AudioFormat format) {
    switch (format) {
      case AudioFormat.wav:
        return 'wav';
      case AudioFormat.aac:
        return 'aac';
      case AudioFormat.opus:
        return 'opus';
    }
  }

  void _simulateAudioData() {
    if (_audioStreamController != null && !_audioStreamController!.isClosed) {
      // Simulate audio data chunks (in a real implementation, this would be actual audio data)
      final data = Uint8List.fromList(List.generate(1024, (i) => i % 256));
      _audioStreamController!.add(data);
    }
  }

  Future<void> _cleanup() async {
    await _recordingDataSubscription?.cancel();
    await _audioStreamController?.close();
    _recordingDataSubscription = null;
    _audioStreamController = null;
    _currentRecordingPath = null;
    _isRecording = false;
    _isPaused = false;
  }

  // Cleanup and dispose
  Future<void> cleanup() async {
    await _cleanup();
    await _recorder?.dispose();
    _recorder = null;
    _isInitialized = false;
  }

  Future<void> dispose() async {
    await cleanup();
  }
}
