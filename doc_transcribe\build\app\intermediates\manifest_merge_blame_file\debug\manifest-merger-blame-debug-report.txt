1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.doc_transcribe"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="23"
8-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml
9        android:targetSdkVersion="34" />
9-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml
10
11    <!-- Permissions -->
12    <uses-permission android:name="android.permission.INTERNET" />
12-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:4:5-67
12-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:4:22-64
13    <uses-permission android:name="android.permission.RECORD_AUDIO" />
13-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:5:5-71
13-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:5:22-68
14    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
14-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:6:5-81
14-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:6:22-78
15    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
15-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:7:5-80
15-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:7:22-77
16    <uses-permission android:name="android.permission.WAKE_LOCK" />
16-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:8:5-68
16-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:8:22-65
17    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
17-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:9:5-77
17-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:9:22-74
18
19    <permission
19-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\b84429f70a7457aa890ea440bb20b385\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
20        android:name="com.example.doc_transcribe.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
20-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\b84429f70a7457aa890ea440bb20b385\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
21        android:protectionLevel="signature" />
21-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\b84429f70a7457aa890ea440bb20b385\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
22
23    <uses-permission android:name="com.example.doc_transcribe.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
23-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\b84429f70a7457aa890ea440bb20b385\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
23-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\b84429f70a7457aa890ea440bb20b385\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
24
25    <application
26        android:name="android.app.Application"
26-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:13:9-42
27        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
27-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\b84429f70a7457aa890ea440bb20b385\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
28        android:debuggable="true"
29        android:extractNativeLibs="false"
30        android:icon="@mipmap/ic_launcher"
30-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:14:9-43
31        android:label="DocTranscribe"
31-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:12:9-38
32        android:requestLegacyExternalStorage="true" >
32-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:15:9-52
33        <activity
33-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:17:9-39:20
34            android:name="com.example.doc_transcribe.MainActivity"
34-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:18:13-41
35            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
35-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:22:13-163
36            android:exported="true"
36-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:19:13-36
37            android:hardwareAccelerated="true"
37-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:23:13-47
38            android:launchMode="singleTop"
38-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:20:13-43
39            android:theme="@style/LaunchTheme"
39-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:21:13-47
40            android:windowSoftInputMode="adjustResize" >
40-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:24:13-55
41
42            <!--
43                 Specifies an Android theme to apply to this Activity as soon as
44                 the Android process has started. This theme is visible to the user
45                 while the Flutter UI initializes. After that, this theme continues
46                 to determine the Window background behind the Flutter UI.
47            -->
48            <meta-data
48-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:30:13-33:17
49                android:name="io.flutter.embedding.android.NormalTheme"
49-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:31:15-70
50                android:resource="@style/NormalTheme" />
50-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:32:15-52
51
52            <intent-filter android:autoVerify="true" >
52-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:35:13-38:29
52-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:35:28-53
53                <action android:name="android.intent.action.MAIN" />
53-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:36:17-68
53-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:36:25-66
54
55                <category android:name="android.intent.category.LAUNCHER" />
55-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:37:17-76
55-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:37:27-74
56            </intent-filter>
57        </activity>
58
59        <!--
60             Don't delete the meta-data below.
61             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
62        -->
63        <meta-data
63-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:43:9-45:33
64            android:name="flutterEmbedding"
64-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:44:13-44
65            android:value="2" />
65-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:45:13-30
66
67        <!-- File provider for sharing files -->
68        <provider
69            android:name="androidx.core.content.FileProvider"
69-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:49:13-62
70            android:authorities="com.example.doc_transcribe.fileprovider"
70-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:50:13-64
71            android:exported="false"
71-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:51:13-37
72            android:grantUriPermissions="true" >
72-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:52:13-47
73            <meta-data
73-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:53:13-55:54
74                android:name="android.support.FILE_PROVIDER_PATHS"
74-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:54:17-67
75                android:resource="@xml/file_paths" />
75-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:55:17-51
76        </provider>
77
78        <uses-library
78-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\68656057c0f9df10db2245ba342a2616\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
79            android:name="androidx.window.extensions"
79-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\68656057c0f9df10db2245ba342a2616\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
80            android:required="false" />
80-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\68656057c0f9df10db2245ba342a2616\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
81        <uses-library
81-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\68656057c0f9df10db2245ba342a2616\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
82            android:name="androidx.window.sidecar"
82-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\68656057c0f9df10db2245ba342a2616\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
83            android:required="false" />
83-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\68656057c0f9df10db2245ba342a2616\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
84
85        <provider
85-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\c5ce1127f562a21d56d5086b663c7f0e\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
86            android:name="androidx.startup.InitializationProvider"
86-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\c5ce1127f562a21d56d5086b663c7f0e\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:25:13-67
87            android:authorities="com.example.doc_transcribe.androidx-startup"
87-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\c5ce1127f562a21d56d5086b663c7f0e\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:26:13-68
88            android:exported="false" >
88-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\c5ce1127f562a21d56d5086b663c7f0e\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:27:13-37
89            <meta-data
89-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\c5ce1127f562a21d56d5086b663c7f0e\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
90                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
90-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\c5ce1127f562a21d56d5086b663c7f0e\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
91                android:value="androidx.startup" />
91-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\c5ce1127f562a21d56d5086b663c7f0e\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
92            <meta-data
92-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\be612b59f18afe430fed3f73124efcee\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
93                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
93-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\be612b59f18afe430fed3f73124efcee\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
94                android:value="androidx.startup" />
94-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\be612b59f18afe430fed3f73124efcee\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
95        </provider>
96
97        <receiver
97-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\be612b59f18afe430fed3f73124efcee\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
98            android:name="androidx.profileinstaller.ProfileInstallReceiver"
98-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\be612b59f18afe430fed3f73124efcee\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
99            android:directBootAware="false"
99-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\be612b59f18afe430fed3f73124efcee\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
100            android:enabled="true"
100-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\be612b59f18afe430fed3f73124efcee\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
101            android:exported="true"
101-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\be612b59f18afe430fed3f73124efcee\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
102            android:permission="android.permission.DUMP" >
102-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\be612b59f18afe430fed3f73124efcee\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
103            <intent-filter>
103-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\be612b59f18afe430fed3f73124efcee\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
104                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
104-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\be612b59f18afe430fed3f73124efcee\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
104-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\be612b59f18afe430fed3f73124efcee\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
105            </intent-filter>
106            <intent-filter>
106-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\be612b59f18afe430fed3f73124efcee\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
107                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
107-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\be612b59f18afe430fed3f73124efcee\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
107-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\be612b59f18afe430fed3f73124efcee\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
108            </intent-filter>
109            <intent-filter>
109-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\be612b59f18afe430fed3f73124efcee\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
110                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
110-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\be612b59f18afe430fed3f73124efcee\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
110-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\be612b59f18afe430fed3f73124efcee\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
111            </intent-filter>
112            <intent-filter>
112-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\be612b59f18afe430fed3f73124efcee\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
113                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
113-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\be612b59f18afe430fed3f73124efcee\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
113-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\be612b59f18afe430fed3f73124efcee\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
114            </intent-filter>
115        </receiver>
116    </application>
117
118</manifest>
