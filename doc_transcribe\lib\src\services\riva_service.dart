import 'dart:async';
import 'dart:io';
import 'dart:typed_data';
import 'package:grpc/grpc.dart';
import '../models/transcript.dart';
import '../utils/uuid_generator.dart';

// Mock gRPC classes for NVIDIA Riva
// In a real implementation, these would be generated from .proto files
class StreamingRecognizeRequest {
  final Uint8List? audioContent;
  final RecognitionConfig? config;
  
  StreamingRecognizeRequest({this.audioContent, this.config});
}

class RecognitionConfig {
  final String encoding;
  final int sampleRateHertz;
  final String languageCode;
  final int audioChannelCount;
  
  RecognitionConfig({
    required this.encoding,
    required this.sampleRateHertz,
    required this.languageCode,
    required this.audioChannelCount,
  });
}

class StreamingRecognizeResponse {
  final List<SpeechRecognitionResult> results;
  
  StreamingRecognizeResponse({required this.results});
}

class SpeechRecognitionResult {
  final List<SpeechRecognitionAlternative> alternatives;
  final bool isFinal;
  
  SpeechRecognitionResult({
    required this.alternatives,
    required this.isFinal,
  });
}

class SpeechRecognitionAlternative {
  final String transcript;
  final double confidence;
  
  SpeechRecognitionAlternative({
    required this.transcript,
    required this.confidence,
  });
}

class RivaService {
  static const String defaultEndpoint = 'grpc.nvcf.nvidia.com:443';
  static const String functionId = 'ee8dc628-76de-4acc-8595-1836e7e857bd'; // Canary-1B ASR
  
  ClientChannel? _channel;
  StreamController<TranscriptSegment>? _transcriptController;
  StreamSubscription? _audioSubscription;
  bool _isConnected = false;
  String? _apiKey;
  String? _language;

  Stream<TranscriptSegment> get transcriptStream => 
      _transcriptController?.stream ?? const Stream.empty();

  bool get isConnected => _isConnected;

  // Initialize Riva service
  Future<void> initialize({
    required String apiKey,
    required String language,
    String? endpoint,
  }) async {
    try {
      _apiKey = apiKey;
      _language = language;
      
      // Create gRPC channel
      _channel = ClientChannel(
        endpoint ?? defaultEndpoint,
        port: 443,
        options: const ChannelOptions(
          credentials: ChannelCredentials.secure(),
        ),
      );

      // Initialize transcript stream
      _transcriptController = StreamController<TranscriptSegment>.broadcast();

      _isConnected = true;
    } catch (e) {
      throw Exception('Failed to initialize Riva service: $e');
    }
  }

  // Start streaming recognition
  Future<void> startStreamingRecognition(Stream<Uint8List> audioStream) async {
    if (!_isConnected || _apiKey == null || _language == null) {
      throw Exception('Riva service not properly initialized');
    }

    try {
      // In a real implementation, this would use the actual gRPC client
      // For now, we'll simulate the streaming recognition
      _audioSubscription = audioStream.listen(
        _processAudioChunk,
        onError: (error) => _transcriptController?.addError(error),
        onDone: () => _finishRecognition(),
      );
    } catch (e) {
      throw Exception('Failed to start streaming recognition: $e');
    }
  }

  // Process audio file (offline transcription)
  Future<List<TranscriptSegment>> transcribeAudioFile(
    String audioFilePath,
    String language,
  ) async {
    if (!_isConnected || _apiKey == null) {
      throw Exception('Riva service not properly initialized');
    }

    try {
      final file = File(audioFilePath);
      if (!await file.exists()) {
        throw Exception('Audio file not found: $audioFilePath');
      }

      // In a real implementation, this would send the file to Riva
      // For now, we'll simulate the transcription process
      await Future.delayed(const Duration(seconds: 2));

      // Generate mock transcript segments
      return _generateMockTranscript();
    } catch (e) {
      throw Exception('Failed to transcribe audio file: $e');
    }
  }

  // Private methods
  void _processAudioChunk(Uint8List audioData) {
    // In a real implementation, this would send audio data to Riva
    // and process the streaming responses
    
    // For simulation, we'll generate mock transcript segments periodically
    Timer.periodic(const Duration(seconds: 2), (timer) {
      if (!_isConnected || _transcriptController?.isClosed == true) {
        timer.cancel();
        return;
      }

      final segment = _generateMockSegment();
      _transcriptController?.add(segment);
    });
  }

  void _finishRecognition() {
    // Send any final transcript segments
    final finalSegment = _generateMockSegment(isFinal: true);
    _transcriptController?.add(finalSegment);
  }

  TranscriptSegment _generateMockSegment({bool isFinal = false}) {
    // Generate mock transcript segments for demonstration
    final mockTexts = [
      "Good morning, how are you feeling today?",
      "I've been having some chest pain since yesterday.",
      "Can you describe the pain? Is it sharp or dull?",
      "It's more of a dull ache, especially when I breathe deeply.",
      "Have you experienced this before?",
      "No, this is the first time.",
      "Let me examine you. Please take a deep breath.",
      "The pain is getting a bit worse now.",
      "I can hear some irregularity in your breathing.",
      "What do you think it could be, doctor?",
    ];

    final speakers = [SpeakerType.doctor, SpeakerType.patient];
    final randomText = mockTexts[DateTime.now().millisecondsSinceEpoch % mockTexts.length];
    final randomSpeaker = speakers[DateTime.now().millisecondsSinceEpoch % speakers.length];

    return TranscriptSegment(
      id: UuidGenerator.generate(),
      timestamp: DateTime.now(),
      speaker: randomSpeaker,
      text: randomText,
      isFinal: isFinal,
      confidence: 0.85 + (DateTime.now().millisecondsSinceEpoch % 15) / 100,
    );
  }

  List<TranscriptSegment> _generateMockTranscript() {
    // Generate a complete mock transcript for file processing
    final segments = <TranscriptSegment>[];
    final baseTime = DateTime.now().subtract(const Duration(minutes: 10));

    final mockConversation = [
      ("Doctor", "Good morning, how are you feeling today?"),
      ("Patient", "I've been having some chest pain since yesterday."),
      ("Doctor", "Can you describe the pain? Is it sharp or dull?"),
      ("Patient", "It's more of a dull ache, especially when I breathe deeply."),
      ("Doctor", "Have you experienced this before?"),
      ("Patient", "No, this is the first time."),
      ("Doctor", "Let me examine you. Please take a deep breath."),
      ("Patient", "The pain is getting a bit worse now."),
      ("Doctor", "I can hear some irregularity in your breathing."),
      ("Patient", "What do you think it could be, doctor?"),
      ("Doctor", "Based on the examination, it could be muscle strain or anxiety-related."),
      ("Patient", "Should I be worried?"),
      ("Doctor", "Let's run some tests to be sure. I'll prescribe some pain relief for now."),
      ("Patient", "Thank you, doctor. When should I come back?"),
      ("Doctor", "Let's schedule a follow-up in one week."),
    ];

    for (int i = 0; i < mockConversation.length; i++) {
      final (speakerLabel, text) = mockConversation[i];
      final speaker = speakerLabel == "Doctor" ? SpeakerType.doctor : SpeakerType.patient;
      final timestamp = baseTime.add(Duration(seconds: i * 30));

      segments.add(TranscriptSegment(
        id: UuidGenerator.generate(),
        timestamp: timestamp,
        speaker: speaker,
        text: text,
        isFinal: true,
        confidence: 0.85 + (i % 15) / 100,
      ));
    }

    return segments;
  }

  // Disconnect from Riva service
  Future<void> disconnect() async {
    try {
      await _audioSubscription?.cancel();
      await _transcriptController?.close();
      await _channel?.shutdown();

      _audioSubscription = null;
      _transcriptController = null;
      _channel = null;
      _isConnected = false;
      _apiKey = null;
      _language = null;
    } catch (e) {
      // Log error but don't throw
      print('Error disconnecting from Riva service: $e');
    }
  }

  // Get supported languages
  static Map<String, String> getSupportedLanguages() {
    return {
      'en-US': 'English (US)',
      'es-ES': 'Spanish (Spain)',
      'hi-IN': 'Hindi (India)',
      'fr-FR': 'French (France)',
      'de-DE': 'German (Germany)',
      'it-IT': 'Italian (Italy)',
      'pt-BR': 'Portuguese (Brazil)',
      'ja-JP': 'Japanese (Japan)',
      'ko-KR': 'Korean (Korea)',
      'zh-CN': 'Chinese (Simplified)',
    };
  }

  // Validate API key format
  static bool isValidApiKey(String apiKey) {
    return apiKey.startsWith('nvapi-') && apiKey.length > 20;
  }
}
